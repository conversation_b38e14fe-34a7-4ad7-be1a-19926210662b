# DataSync Development Patterns

This document provides a focused overview of the key patterns specifically relevant to the N4-DataSync module development. For a comprehensive list of all Niagara patterns, see `/docs/Development/NiagaraPatterns.md`.

## Core Patterns for DataSync

### 1. Component Model Pattern
Essential for creating the `BConnectionProfile` and IDM components that mirror Niagara's structure.

### 2. Persistence Strategy Pattern
Critical for implementing the dual persistence approach (component tree + JSON files) for connection profiles.

```java
// Example from ProfileManager
public boolean saveProfile(BConnectionProfile profile, String name) {
  try {
    // Create profiles directory if it doesn't exist
    File profilesDir = getProfilesDirectory();
    if (!profilesDir.exists()) {
      profilesDir.mkdirs();
    }
    
    // Convert profile to JSON
    Gson gson = new GsonBuilder().setPrettyPrinting().create();
    String json = gson.toJson(profile);
    
    // Write to file
    File profileFile = new File(profilesDir, sanitizeFileName(name) + ".json");
    FileWriter writer = new FileWriter(profileFile);
    writer.write(json);
    writer.close();
    
    return true;
  } catch (Exception e) {
    System.err.println("Error saving profile: " + e.getMessage());
    return false;
  }
}
```

### 3. Workbench Tool Pattern
For creating the DataSync tool entry in the Workbench Tools menu.

```java
@NiagaraType
@AgentOn(types = "workbench:Workbench")
public class BDataSyncTool extends BWbNavNodeTool {
  // Implementation
}
```

### 4. Adapter Pattern
For converting external data sources (Excel, etc.) to the Intermediate Data Model.

```java
public class ExcelSourceReader implements IExternalSourceReader {
  @Override
  public List<IdmComponent> readSource(String path) {
    // Read Excel file and convert to IDM
    // ...
    return idmComponents;
  }
}
```

### 5. Fox Remote Programming Pattern
For interacting with remote Niagara stations to create/update components.

```java
public void createComponent(IdmComponent component, String targetPath) {
  try {
    // Connect to station
    BOrd stationOrd = BOrd.make("station:|" + targetPath);
    BComponent parent = (BComponent)stationOrd.get();
    
    // Create component
    BComponent newComponent = BComponent.make(component.getType());
    newComponent.setDisplayName(component.getName());
    
    // Set properties
    // ...
    
    // Add to parent
    parent.add(component.getName(), newComponent);
  } catch (Exception e) {
    // Handle error
  }
}
```

### 6. Module-View-Controller (MVC) Variant
For structuring the UI components of the DataSync tool.

```java
public class DataSyncModel extends MgrModel {
  // Data and business logic
}

public class DataSyncController extends MgrController {
  // User input handling
}

public class DataSyncState extends MgrState {
  // UI state
}
```

### 7. Factory Pattern
For creating appropriate reader implementations based on source type.

```java
public class ReaderFactory {
  public static IExternalSourceReader createReader(String sourceType) {
    switch (sourceType) {
      case "Excel":
        return new ExcelSourceReader();
      case "CSV":
        return new CsvSourceReader();
      // Other types
      default:
        throw new IllegalArgumentException("Unknown source type: " + sourceType);
    }
  }
}
```

### 8. Composite Pattern
For modeling the hierarchical IDM that mirrors Niagara's component tree.

```java
public abstract class IdmComponent {
  private String name;
  private String type;
  private Map<String, Object> properties = new HashMap<>();
  private List<IdmComponent> children = new ArrayList<>();
  
  // Methods for adding/getting children
  public void addChild(IdmComponent child) {
    children.add(child);
  }
  
  public List<IdmComponent> getChildren() {
    return children;
  }
  
  // Other methods
}
```

### 9. Niagara Test Pattern
For creating automated tests for the DataSync module.

```java
@Test(groups = {"datasync", "unit", "json"})
public void testProfileSerialization() {
  // Test implementation
}
```

### 10. Command Pattern
For encapsulating synchronization operations.

```java
public interface SyncCommand {
  void execute();
  void undo();
  String getDescription();
}

public class CreateComponentCommand implements SyncCommand {
  private IdmComponent component;
  private String targetPath;
  private NiagaraFoxSyncWriter writer;
  
  // Constructor
  
  @Override
  public void execute() {
    writer.createComponent(component, targetPath);
  }
  
  @Override
  public void undo() {
    writer.deleteComponent(targetPath + "/" + component.getName());
  }
  
  @Override
  public String getDescription() {
    return "Create " + component.getType() + " '" + component.getName() + "'";
  }
}
```

## Implementation Priorities

For the N4-DataSync V1 MVP, focus on these patterns in order:

1. **Component Model Pattern** - For `BConnectionProfile`
2. **Workbench Tool Pattern** - For the tool entry point
3. **Persistence Strategy Pattern** - For profile management
4. **Module-View-Controller (MVC) Variant** - For the UI
5. **Adapter Pattern** - For Excel data reading
6. **Fox Remote Programming Pattern** - For component creation
7. **Factory Pattern** - For source reader creation
8. **Composite Pattern** - For the IDM
9. **Niagara Test Pattern** - For testing
10. **Command Pattern** - For sync operations

## References

- See full pattern descriptions in `/docs/Development/NiagaraPatterns.md`
- See use case organization in `/docs/Development/PatternsByUseCase.md`
- N4-DataSync Full Feature Specification & Roadmap
- Niagara Developer Documentation