<?xml version="1.0" encoding="UTF-8"?>
<testng-results skipped="0" failed="0" ignored="4" total="6" passed="2">
  <reporter-output>
  </reporter-output>
  <suite name="datasyncTest_ProfileManagerTest" duration-ms="743" started-at="2025-06-28T11:03:31Z" finished-at="2025-06-28T11:03:32Z">
    <groups>
      <group name="unit">
        <method signature="BProfileManagerTest.testCompletePersistenceCycle()[pri:0, instance:Profile Manager Test]" name="testCompletePersistenceCycle" class="com.mea.datasync.test.BProfileManagerTest"/>
        <method signature="BProfileManagerTest.testProfileManagerBasicOperations()[pri:0, instance:Profile Manager Test]" name="testProfileManagerBasicOperations" class="com.mea.datasync.test.BProfileManagerTest"/>
      </group> <!-- unit -->
      <group name="datasync">
        <method signature="BProfileManagerTest.testCompletePersistenceCycle()[pri:0, instance:Profile Manager Test]" name="testCompletePersistenceCycle" class="com.mea.datasync.test.BProfileManagerTest"/>
        <method signature="BProfileManagerTest.testProfileManagerBasicOperations()[pri:0, instance:Profile Manager Test]" name="testProfileManagerBasicOperations" class="com.mea.datasync.test.BProfileManagerTest"/>
      </group> <!-- datasync -->
      <group name="standalone">
        <method signature="BProfileManagerTest.testCompletePersistenceCycle()[pri:0, instance:Profile Manager Test]" name="testCompletePersistenceCycle" class="com.mea.datasync.test.BProfileManagerTest"/>
        <method signature="BProfileManagerTest.testProfileManagerBasicOperations()[pri:0, instance:Profile Manager Test]" name="testProfileManagerBasicOperations" class="com.mea.datasync.test.BProfileManagerTest"/>
      </group> <!-- standalone -->
      <group name="persistence">
        <method signature="BProfileManagerTest.testCompletePersistenceCycle()[pri:0, instance:Profile Manager Test]" name="testCompletePersistenceCycle" class="com.mea.datasync.test.BProfileManagerTest"/>
      </group> <!-- persistence -->
      <group name="filesystem">
        <method signature="BProfileManagerTest.testProfileManagerBasicOperations()[pri:0, instance:Profile Manager Test]" name="testProfileManagerBasicOperations" class="com.mea.datasync.test.BProfileManagerTest"/>
      </group> <!-- filesystem -->
    </groups>
    <test name="Command line test" duration-ms="743" started-at="2025-06-28T11:03:31Z" finished-at="2025-06-28T11:03:32Z">
      <class name="com.mea.datasync.test.BProfileManagerTest">
        <test-method status="PASS" signature="setupBeforeClass()[pri:0, instance:Profile Manager Test]" name="setupBeforeClass" is-config="true" duration-ms="133" started-at="2025-06-28T11:03:31Z" finished-at="2025-06-28T11:03:31Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setupBeforeClass -->
        <test-method status="PASS" signature="setClassLoader()[pri:0, instance:Profile Manager Test]" name="setClassLoader" is-config="true" duration-ms="1" started-at="2025-06-28T11:03:31Z" finished-at="2025-06-28T11:03:31Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setClassLoader -->
        <test-method status="PASS" signature="testCompletePersistenceCycle()[pri:0, instance:Profile Manager Test]" name="testCompletePersistenceCycle" duration-ms="540" started-at="2025-06-28T11:03:31Z" description="Test complete profile persistence cycle" finished-at="2025-06-28T11:03:32Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testCompletePersistenceCycle -->
        <test-method status="PASS" signature="restoreClassLoader()[pri:0, instance:Profile Manager Test]" name="restoreClassLoader" is-config="true" duration-ms="1" started-at="2025-06-28T11:03:32Z" finished-at="2025-06-28T11:03:32Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- restoreClassLoader -->
        <test-method status="PASS" signature="setClassLoader()[pri:0, instance:Profile Manager Test]" name="setClassLoader" is-config="true" duration-ms="0" started-at="2025-06-28T11:03:32Z" finished-at="2025-06-28T11:03:32Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- setClassLoader -->
        <test-method status="PASS" signature="testProfileManagerBasicOperations()[pri:0, instance:Profile Manager Test]" name="testProfileManagerBasicOperations" duration-ms="29" started-at="2025-06-28T11:03:32Z" description="Test ProfileManager basic directory operations" finished-at="2025-06-28T11:03:32Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- testProfileManagerBasicOperations -->
        <test-method status="PASS" signature="restoreClassLoader()[pri:0, instance:Profile Manager Test]" name="restoreClassLoader" is-config="true" duration-ms="2" started-at="2025-06-28T11:03:32Z" finished-at="2025-06-28T11:03:32Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- restoreClassLoader -->
        <test-method status="PASS" signature="teardownAfterClass()[pri:0, instance:Profile Manager Test]" name="teardownAfterClass" is-config="true" duration-ms="1" started-at="2025-06-28T11:03:32Z" finished-at="2025-06-28T11:03:32Z">
          <reporter-output>
          </reporter-output>
        </test-method> <!-- teardownAfterClass -->
      </class> <!-- com.mea.datasync.test.BProfileManagerTest -->
    </test> <!-- Command line test -->
  </suite> <!-- datasyncTest_ProfileManagerTest -->
</testng-results>
