# N4-DataSync Developer Documentation

## Getting Started

* **[Quick Start Guide](QUICK_START.md)** - Get up and running in 5 minutes
* **[Certificate Setup](CERTIFICATE_SETUP.md)** - How to set up module signing certificates for N4-DataSync development
* **[Testing Guide](TESTING.md)** - How to run tests and understand test results for N4-DataSync development
* **[Troubleshooting](TROUBLESHOOTING.md)** - Common issues and solutions
* **[Contributing Guide](CONTRIBUTING.md)** - How to contribute to the project

## Development Patterns

* **[Niagara Patterns](NiagaraPatterns.md)** - Essential patterns for Niagara module development
* **[Patterns by Use Case](PatternsByUseCase.md)** - Quick reference for implementing specific features
* **[Architecture](ARCHITECTURE.md)** - System design and component relationships

## Technical Reference

* **[JSON Schemas](schemas/)** - Data structure definitions and validation schemas

## Official Niagara Documentation

* **[Niagara Framework Documentation](Niagara/)** - Complete Niagara framework documentation
* **[Niagara Developer Guide](Niagara/Niagara%20Developer%20Guide%20Index.md)** - Official Niagara developer guide

## Reference Materials

* **[Niagara Source Code](../niagara_source_code/)** - Official Niagara ********* source code for development reference

## Project Documentation

* **[N4-DataSync Specification](../N4-DataSync%20Full%20Feature%20Specification%20&%20Roadmap.md)** - Complete project specification and roadmap
* **[Project README](../README.md)** - Main project overview and installation guide
